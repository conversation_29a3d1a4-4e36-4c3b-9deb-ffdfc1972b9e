//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SistemaInfo.Processadora.Biz.Service.Languages {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class BizMensagens {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal BizMensagens() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("SistemaInfo.Processadora.Biz.Service.Languages.BizMensagens", typeof(BizMensagens).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Para realizar a conciliação vinculado processadora x plataforma é obrigatório informar o tipo de produto!.
        /// </summary>
        internal static string ConciliacaoProdutoIdNaoInformado {
            get {
                return ResourceManager.GetString("ConciliacaoProdutoIdNaoInformado", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Biz Informa: .
        /// </summary>
        internal static string ProcessadoraInforma {
            get {
                return ResourceManager.GetString("ProcessadoraInforma", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Senha inválida. A senha deve conter apenas números e possuir 4 dígitos..
        /// </summary>
        internal static string SenhaInvalida {
            get {
                return ResourceManager.GetString("SenhaInvalida", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transação pendente de confirmação com a processadora de cartões..
        /// </summary>
        internal static string TransacaoPendente {
            get {
                return ResourceManager.GetString("TransacaoPendente", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Web Service processado com sucesso..
        /// </summary>
        internal static string WebServiceAtivo {
            get {
                return ResourceManager.GetString("WebServiceAtivo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [Serviço da Processadora] .
        /// </summary>
        internal static string WebServiceErro {
            get {
                return ResourceManager.GetString("WebServiceErro", resourceCulture);
            }
        }
    }
}
